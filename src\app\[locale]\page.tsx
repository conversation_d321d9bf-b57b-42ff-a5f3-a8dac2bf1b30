"use client";

import { motion, useScroll, useSpring, useTransform } from "framer-motion";
import { useEffect, useState } from "react";
import Navigation from "@/components/Navigation";
import Hero from "@/components/sections/Hero";
import FeaturesSection from "@/components/sections/FeaturesSection";
import ScreenshotsSection from "@/components/sections/ScreenshotsSection";
import CTASection from "@/components/sections/CTASection";
import FooterSection from "@/components/sections/FooterSection";
import ScrollToCTA from "@/components/ScrollToCTA";

export default function HomePage() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001,
  });

  // 页面背景动画
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const backgroundOpacity = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    [1, 0.8, 0.6]
  );

  // 固定的粒子位置数据，避免SSR水合错误
  const particlePositions = [
    { left: 15, top: 20 },
    { left: 85, top: 15 },
    { left: 25, top: 80 },
    { left: 75, top: 70 },
    { left: 45, top: 30 },
    { left: 65, top: 85 },
    { left: 10, top: 60 },
    { left: 90, top: 40 },
    { left: 35, top: 10 },
    { left: 55, top: 90 },
    { left: 20, top: 45 },
    { left: 80, top: 25 },
    { left: 40, top: 75 },
    { left: 70, top: 55 },
    { left: 30, top: 35 },
    { left: 60, top: 65 },
    { left: 5, top: 50 },
    { left: 95, top: 30 },
    { left: 50, top: 5 },
    { left: 25, top: 95 },
  ];

  useEffect(() => {
    // 防止初始滚动条闪现
    document.body.style.overflow = "hidden";

    setIsMounted(true);
    // 页面加载动画
    const timer = setTimeout(() => {
      setIsLoaded(true);
      // 恢复滚动
      document.body.style.overflow = "auto";
    }, 200);

    return () => {
      clearTimeout(timer);
      // 清理时恢复滚动
      document.body.style.overflow = "auto";
    };
  }, []);

  // 页面级动画变体
  const pageVariants = {
    initial: {
      opacity: 0,
    },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.1, 0.35, 1] as const,
        staggerChildren: 0.1,
      },
    },
  };

  const sectionVariants = {
    initial: {
      opacity: 0,
      y: 20,
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.1, 0.35, 1] as const,
      },
    },
  };

  // 防止初始闪现，等待完全挂载
  if (!isMounted) {
    return <div className="fixed inset-0 bg-bg-primary z-[10000]" />;
  }

  return (
    <motion.div
      className="min-h-screen w-full max-w-full overflow-x-hidden relative"
      variants={pageVariants}
      initial="initial"
      animate={isLoaded ? "animate" : "initial"}
      style={{
        maxWidth: "100vw",
        overflowX: "hidden",
      }}
    >
      {/* 动态背景层 */}
      <motion.div
        className="fixed inset-0 pointer-events-none z-0"
        style={{
          y: backgroundY,
          opacity: backgroundOpacity,
        }}
      >
        {/* 渐变背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 via-purple-500/5 to-cyan-500/5" />

        {/* 动态粒子效果 */}
        {isMounted && (
          <div className="absolute inset-0">
            {particlePositions.map((position, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-white/20 rounded-full"
                style={{
                  left: `${position.left}%`,
                  top: `${position.top}%`,
                }}
                animate={{
                  y: [0, -100, 0],
                  opacity: [0, 1, 0],
                }}
                transition={{
                  duration: 3 + (i % 3),
                  repeat: Infinity,
                  delay: i * 0.2,
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>
        )}
      </motion.div>

      {/* 滚动进度条 */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-red-500 via-purple-500 to-cyan-500 z-[9999] origin-left shadow-lg"
        style={{
          scaleX,
          boxShadow:
            "0 0 10px rgba(255, 46, 77, 0.5), 0 0 20px rgba(124, 58, 237, 0.3)",
        }}
      />

      {/* 页面内容 */}
      <div className="relative z-10 w-full max-w-full overflow-x-hidden">
        <ScrollToCTA />

        <motion.div variants={sectionVariants}>
          <Navigation />
        </motion.div>

        <motion.div variants={sectionVariants}>
          <Hero />
        </motion.div>

        <motion.div variants={sectionVariants}>
          <FeaturesSection />
        </motion.div>

        <motion.div variants={sectionVariants}>
          <ScreenshotsSection />
        </motion.div>

        <motion.div variants={sectionVariants}>
          <CTASection />
        </motion.div>

        <motion.div variants={sectionVariants}>
          <FooterSection />
        </motion.div>
      </div>

      {/* 鼠标跟随光效 */}
      <MouseFollowLight />
    </motion.div>
  );
}

// 鼠标跟随光效组件
function MouseFollowLight() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    if (typeof window !== "undefined") {
      window.addEventListener("mousemove", updateMousePosition);
      return () => window.removeEventListener("mousemove", updateMousePosition);
    }
  }, []);

  if (!isClient) {
    return null;
  }

  return (
    <motion.div
      className="fixed pointer-events-none z-30"
      style={{
        left: mousePosition.x - 100,
        top: mousePosition.y - 100,
      }}
      animate={{
        scale: [1, 1.2, 1],
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    >
      <div className="w-48 h-48 bg-gradient-radial from-red-500/10 via-purple-500/5 to-transparent rounded-full blur-xl" />
    </motion.div>
  );
}
