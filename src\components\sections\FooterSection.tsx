"use client";

import { motion, useInView } from "framer-motion";
import { useRef, useState, useEffect } from "react";
import { useTranslations } from "next-intl";

export default function FooterSection() {
  const tFooter = useTranslations("Footer");
  const tNav = useTranslations("Navigation");
  const tCommon = useTranslations("Common");
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [isMounted, setIsMounted] = useState(false);

  // 固定的粒子位置数据
  const particlePositions = [
    { left: 10, top: 20 },
    { left: 80, top: 15 },
    { left: 30, top: 70 },
    { left: 60, top: 80 },
    { left: 20, top: 40 },
    { left: 90, top: 60 },
    { left: 15, top: 85 },
    { left: 70, top: 25 },
    { left: 40, top: 10 },
    { left: 85, top: 90 },
  ];

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 容器动画变体
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  // 项目动画变体
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.1, 0.35, 1] as const,
      },
    },
  };

  // Logo层动画变体
  const logoLayerVariants = {
    animate: (i: number) => ({
      rotate: [0, 360],
      scale: [1, 1.1, 1],
      transition: {
        duration: 8 + i * 2,
        repeat: Infinity,
        ease: "linear" as const,
        delay: i * 0.5,
      },
    }),
  };

  // 链接悬停动画
  const linkVariants = {
    hover: {
      scale: 1.05,
      y: -2,
      transition: { duration: 0.2 },
    },
    tap: {
      scale: 0.95,
      transition: { duration: 0.1 },
    },
  };

  return (
    <motion.footer
      className="py-16 bg-bg-primary md:py-12 relative overflow-hidden"
      ref={ref}
    >
      {/* 背景动画效果 */}
      {isMounted && (
        <div className="absolute inset-0 pointer-events-none">
          {particlePositions.map((position, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-gradient-to-r from-red-500/20 to-purple-500/20 rounded-full"
              style={{
                left: `${position.left}%`,
                top: `${position.top}%`,
              }}
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 0.6, 0],
              }}
              transition={{
                duration: 4 + (i % 2),
                repeat: Infinity,
                delay: i * 0.3,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>
      )}

      <div className="max-w-6xl mx-auto px-5 relative z-10">
        <motion.div
          className="flex justify-between items-center mb-8 flex-col md:flex-row gap-8 md:gap-0"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          <motion.div
            className="flex flex-col gap-2 text-center md:text-left"
            variants={itemVariants}
          >
            <motion.div
              className="flex items-center gap-3 justify-center md:justify-start"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                className="w-8 h-8 relative"
                whileHover={{ rotate: 180 }}
                transition={{ duration: 0.6 }}
              >
                <div className="relative w-full h-full">
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-red-500 to-purple-600 rounded-lg opacity-80"
                    variants={logoLayerVariants}
                    animate="animate"
                    custom={0}
                  />
                  <motion.div
                    className="absolute inset-0.5 bg-gradient-to-br from-purple-600 to-blue-500 rounded-lg opacity-60"
                    variants={logoLayerVariants}
                    animate="animate"
                    custom={1}
                  />
                  <motion.div
                    className="absolute inset-1 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg opacity-40"
                    variants={logoLayerVariants}
                    animate="animate"
                    custom={2}
                  />
                </div>
              </motion.div>
              <motion.span
                className="text-xl font-bold text-white"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {tCommon("brandName")}
              </motion.span>
            </motion.div>
            <motion.p
              className="text-gray-400 text-sm m-0"
              variants={itemVariants}
            >
              {tCommon("tagline")}
            </motion.p>
          </motion.div>

          <motion.div
            className="flex gap-8 flex-wrap justify-center md:justify-end"
            variants={itemVariants}
          >
            {[
              { key: "privacy", text: tNav("privacy") },
              { key: "terms", text: tNav("terms") },
              { key: "contact", text: tNav("contact") },
              { key: "twitter", text: tNav("twitter") },
            ].map((link, index) => (
              <motion.a
                key={link.key}
                href="#"
                className="text-gray-300 text-sm hover:text-red-500 transition-colors duration-300 relative"
                variants={linkVariants}
                whileHover="hover"
                whileTap="tap"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 + index * 0.1, duration: 0.4 }}
              >
                {link.text}

                {/* 悬停下划线效果 */}
                <motion.div
                  className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-red-500 to-purple-500"
                  initial={{ scaleX: 0 }}
                  whileHover={{ scaleX: 1 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.a>
            ))}
          </motion.div>
        </motion.div>

        {/* Footer Bottom */}
        <motion.div
          className="text-center pt-8 border-t border-border relative"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          {/* 边框光效 */}
          <motion.div
            className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-px bg-gradient-to-r from-transparent via-red-500/50 to-transparent"
            initial={{ scaleX: 0 }}
            animate={isInView ? { scaleX: 1 } : { scaleX: 0 }}
            transition={{ delay: 0.6, duration: 0.8 }}
          />

          <motion.p
            className="text-gray-400 text-sm m-0"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ delay: 1, duration: 0.6 }}
          >
            {tFooter("copyright")}
          </motion.p>
        </motion.div>
      </div>
    </motion.footer>
  );
}
